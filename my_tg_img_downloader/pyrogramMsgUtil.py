import datetime

import requests
import os
import pyrogram
from pyrogram import Client, filters
from mongo.MongoDBHelper import MongoDBHelper
from my_tg_img_downloader import telegram_img_download

# 获取Telegram Bot API的token
# bot_token = '**********************************************'
# 准备下载目录
download_dir = '/Volumes/新加卷/tg-image'
# download_dir = '/Users/<USER>/Desktop/test/'
api_hash= '792cfdcecaeab811010c87c98c200ec7'
api_id= '21786636'

app = pyrogram.Client(
        "media_downloader",
        api_id=api_id,
        api_hash=api_hash,
        # bot_token=bot_token,
        proxy={
            "hostname": "127.0.0.1",
            "port": 7890,
            "scheme": "socks5"
        })

def get_img_message_id(channel, def_msg_id, debug=False):
    print(f"start process channel{channel}")
    c = MongoDBHelper()
    videos = c.my_find_data('tele_img', {"channel": channel, "tg_msg_id": {"$ne": None}})
    init_load = True
    message_info = None
    if debug or videos is None or len(videos) == 0:
        last_msg_id = def_msg_id + 1
    else:
        last_msg_id = max(v["tg_msg_id"] for v in videos) + 1
    with app:
        while message_info is None or message_info[len(message_info)-1].empty is None:
            init_load = False
            message_ids = [last_msg_id + x for x in range(100)]
            message_info = list(app.get_messages(channel, message_ids=message_ids))
            last_msg_id = message_info[len(message_info)-1].id

            message_info_f = list(filter(lambda x: x.entities is not None and len(x.entities)>0 and \
                    (x.entities[0].url is not None or x.entities[len(x.entities)-1].url is not None), message_info))
            print(f"{len(message_info_f)} collections found.")
            for msg in message_info_f:
                try:
                    url = msg.entities[0].url or msg.entities[len(msg.entities)-1].url
                    title = msg.text.split('\n')[-1]
                    if title.startswith('番号：'):
                        title = title[3:]
                    print(f"start download {title}.")
                    file_name = download_dir + "/" + title
                    telegram_img_download.load_page2(url, title, download_dir)
                    c.my_insert_data('tele_img', {
                        "link": url,
                        "title": title,
                        "channel": channel,
                        "date": datetime.datetime.now().strftime('%Y-%m-%d'),
                        "tg_msg_id": msg.id,
                        "status": "Success",
                        "path": file_name
                    })
                except Exception as e:
                    print(e)
                    c.my_insert_data('tele_img', {
                        "link": url,
                        "title": title,
                        "channel": channel,
                        "date": datetime.datetime.now().strftime('%Y-%m-%d'),
                        "tg_msg_id": msg.id,
                        "status": str(e),
                        "path": file_name
                    })

def run():
    # get_img_message_id('AnchorPic', 4998)
    get_img_message_id('MarioBase', 45130, False)
    telegram_img_download.startScrapy()

if __name__ == '__main__':
    run()